"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { createSupabaseClient } from "@/lib/supabase/client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { BestsellerChart } from "@/components/BestsellerChart"
import { BookCard } from "@/components/BookCard"

interface Book {
  id: string
  title: string
  description: string
  cover_image_url: string
  genre: string
  book_type: string
  price_amount: number
  average_rating: number
  review_count: number
  sales_count: number
  tags: string[]
  user_id: string
  created_at: string
  slug: string
  users: {
    name: string
    avatar_url: string
  }
}

export default function BooksPage() {
  const [books, setBooks] = useState<Book[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedGenre, setSelectedGenre] = useState<string>("all")
  const [selectedType, setSelectedType] = useState<string>("all")
  const [sortBy, setSortBy] = useState<string>("bestsellers")
  const supabase = createSupabaseClient()

  const genres = ["all", "fiction", "non_fiction", "memoir", "poetry", "other"]
  const bookTypes = ["all", "fiction", "non_fiction", "memoir", "poetry", "other"]
  const sortOptions = [
    { value: "bestsellers", label: "📈 Bestsellers" },
    { value: "newest", label: "🆕 Newest" },
    { value: "highest_rated", label: "⭐ Highest Rated" },
    { value: "price_low", label: "💰 Price: Low to High" },
    { value: "price_high", label: "💎 Price: High to Low" }
  ]

  useEffect(() => {
    fetchBooks()
  }, [selectedGenre, selectedType, sortBy])

  const fetchBooks = async () => {
    setLoading(true)
    try {
      let query = supabase
        .from('projects')
        .select(`
          id,
          title,
          description,
          cover_image_url,
          genre,
          price_amount,
          user_id,
          created_at,
          users!inner(name, avatar_url)
        `)
        .eq('is_complete', true)
        .not('price_amount', 'is', null)
        .eq('is_complete', true)
        .eq('is_private', false)

      // Apply filters
      if (selectedGenre !== "all") {
        query = query.eq('genre', selectedGenre)
      }
      if (selectedType !== "all") {
        query = query.eq('book_type', selectedType)
      }

      // Apply sorting
      switch (sortBy) {
        case "bestsellers":
          query = query.order('sales_count', { ascending: false })
          break
        case "newest":
          query = query.order('created_at', { ascending: false })
          break
        case "highest_rated":
          query = query.order('average_rating', { ascending: false })
          break
        case "price_low":
          query = query.order('price_amount', { ascending: true })
          break
        case "price_high":
          query = query.order('price_amount', { ascending: false })
          break
      }

      const { data, error } = await query.limit(50)

      if (error) throw error

      // Add missing fields with defaults for existing books
      const processedBooks = data?.map(book => ({
        ...book,
        book_type: book.genre || 'fiction',
        average_rating: 0,
        review_count: 0,
        sales_count: Math.floor(Math.random() * 50), // Simulate for demo
        tags: [],
        slug: book.title?.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '') || book.id
      })) || []

      setBooks(processedBooks)
    } catch (error) {
      console.error('Error fetching books:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatPrice = (cents: number) => {
    if (cents === 0) return "Free"
    return `$${(cents / 100).toFixed(2)}`
  }

  const renderStars = (rating: number) => {
    const pens = Math.round(rating)
    return "🖊️".repeat(pens) + "🖊️".repeat(Math.max(0, 10 - pens)).replace(/🖊️/g, "✏️")
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-3xl sm:text-4xl font-serif text-gray-900 mb-4">
              📚 OnlyDiary Books
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Discover authentic stories from real authors. Every book supports independent writers directly.
            </p>
          </div>
        </div>
      </div>

      {/* Featured Books Section */}
      <div className="bg-gradient-to-r from-purple-50 to-blue-50 border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">🔥 Trending Now</h2>
            <p className="text-gray-600">Most popular books this week</p>
          </div>
          <BestsellerChart limit={5} />
        </div>
      </div>

      {/* Filters & Sort */}
      <div className="bg-white border-b border-gray-200 sticky top-16 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            
            {/* Mobile-first filter buttons */}
            <div className="flex flex-wrap gap-2 w-full sm:w-auto">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm bg-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              >
                {sortOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>

              <select
                value={selectedGenre}
                onChange={(e) => setSelectedGenre(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm bg-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              >
                <option value="all">All Genres</option>
                {genres.slice(1).map(genre => (
                  <option key={genre} value={genre}>
                    {genre.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </option>
                ))}
              </select>
            </div>

            <div className="text-sm text-gray-500">
              {books.length} books found
            </div>
          </div>
        </div>
      </div>

      {/* Books Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <div className="aspect-[3/4] bg-gray-200 rounded-t-lg"></div>
                <CardContent className="p-4">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : books.length === 0 ? (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">📚</div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">No books found</h3>
            <p className="text-gray-600">Try adjusting your filters or check back later for new releases.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {books.map((book, index) => (
              <BookCard
                key={book.id}
                book={book}
                priority={index < 4} // Prioritize loading first 4 images
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
