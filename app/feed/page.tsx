'use client'

import { useState, useEffect, useCallback } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { redirect } from 'next/navigation'
import Link from 'next/link'
import { Heart, MessageCircle, Clock, CreditCard } from 'lucide-react'
import Image from 'next/image'

interface DiaryEntry {
  id: string
  title: string
  body_md: string
  created_at: string
  is_free: boolean
  bundle_count: number
  user: {
    id: string
    name: string
    avatar?: string
  }
  loves_count: number
  comments_count: number
  has_access: boolean
  credits_required: number
}

interface UserCredits {
  [userId: string]: {
    credits_remaining: number
    last_reset: string
  }
}

export default function FeedPage() {
  const [user, setUser] = useState<{ id: string; role: string } | null>(null)
  const [entries, setEntries] = useState<DiaryEntry[]>([])
  const [userCredits, setUserCredits] = useState<UserCredits>({})
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()

  const checkUser = useCallback(async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        redirect('/login')
        return
      }

      // Get user profile
      const { data: profile } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single()

      if (!profile || profile.role !== 'subscriber') {
        redirect('/dashboard')
        return
      }

      setUser(profile)
      await loadFeed(user.id)
    } catch (error) {
      console.error('Error checking user:', error)
      redirect('/login')
    }
  }, [supabase])

  useEffect(() => {
    checkUser()
  }, [checkUser])

  const loadFeed = async (userId: string) => {
    try {
      // For now, let's show all writers since subscriptions might be empty
      // Later we'll filter by actual subscriptions
      const { data: writers } = await supabase
        .from('users')
        .select('id')
        .eq('role', 'writer')

      if (!writers || writers.length === 0) {
        setLoading(false)
        return
      }

      const writerIds = writers.map(writer => writer.id)

      // Get recent entries from subscribed writers
      const { data: feedEntries } = await supabase
        .from('diary_entries')
        .select(`
          id,
          title,
          body_md,
          created_at,
          is_free,
          bundle_count,
          user_id,
          user:users!user_id (
            id,
            name,
            avatar
          ),
          loves_count,
          comments_count
        `)
        .in('user_id', writerIds)
        .eq('is_published', true)
        .order('created_at', { ascending: false })
        .limit(50)

      // Get user's credits for each writer
      const { data: credits } = await supabase
        .from('post_credits')
        .select('*')
        .eq('user_id', userId)
        .in('writer_id', writerIds)

      // Process credits
      const creditsMap: UserCredits = {}
      credits?.forEach(credit => {
        creditsMap[credit.writer_id] = {
          credits_remaining: credit.credits_remaining,
          last_reset: credit.expires_at || credit.created_at
        }
      })

      // Check access for each entry
      const entriesWithAccess = feedEntries?.map(entry => ({
        ...entry,
        user: entry.user[0],
        has_access: entry.is_free || (creditsMap[entry.user_id]?.credits_remaining || 0) >= (entry.bundle_count || 1),
        credits_required: entry.bundle_count || 1
      })) || []

      setEntries(entriesWithAccess)
      setUserCredits(creditsMap)
    } catch (error) {
      console.error('Error loading feed:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleReadEntry = async (entry: DiaryEntry) => {
    if (entry.is_free || entry.has_access) {
      // User can read this entry
      window.location.href = `/d/${entry.id}`
    } else {
      // Need to use credits or subscribe
      const creditsNeeded = entry.credits_required
      const currentCredits = userCredits[entry.user.id]?.credits_remaining || 0
      
      if (currentCredits < creditsNeeded) {
        // Need to subscribe for more credits
        if (confirm(`You need ${creditsNeeded} credits to read this entry. You have ${currentCredits} credits remaining. Would you like to subscribe for 30 more credits?`)) {
          window.location.href = `/u/${entry.user.id}?subscribe=true`
        }
      } else {
        // Use credits to unlock
        if (confirm(`This will use ${creditsNeeded} of your ${currentCredits} credits. Continue?`)) {
          try {
            const { error } = await supabase
              .from('post_credits')
              .update({
                credits_remaining: currentCredits - creditsNeeded
              })
              .eq('user_id', user!.id)
              .eq('writer_id', entry.user.id)

            if (!error) {
              window.location.href = `/d/${entry.id}`
            }
          } catch (err) {
            console.error('Error using credits:', err)
          }
        }
      }
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-6 py-8">
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-serif text-gray-800 mb-2">
            Your Reading Feed
          </h1>
          <p className="text-gray-600 font-serif">
            Latest stories from creators you follow
          </p>
        </div>

        {/* Credits Summary */}
        <div className="bg-white rounded-2xl p-6 shadow-sm mb-8">
          <h2 className="text-lg font-serif text-gray-800 mb-4">Your Credits</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Object.entries(userCredits).map(([writerId, credits]) => {
              const writer = entries.find(e => e.user.id === writerId)?.user
              return writer ? (
                <div key={writerId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                      {writer.avatar ? (
                        <Image src={writer.avatar} alt={writer.name} width={32} height={32} className="w-full h-full object-cover" />
                      ) : (
                        <span className="text-sm font-serif text-gray-500">
                          {writer.name.charAt(0).toUpperCase()}
                        </span>
                      )}
                    </div>
                    <span className="font-medium text-gray-800 text-sm">{writer.name}</span>
                  </div>
                  <div className="flex items-center gap-1 text-blue-600">
                    <CreditCard className="w-4 h-4" />
                    <span className="font-bold">{credits.credits_remaining}</span>
                  </div>
                </div>
              ) : null
            })}
          </div>
        </div>

        {/* Feed */}
        {entries.length > 0 ? (
          <div className="space-y-6">
            {entries.map((entry) => (
              <div key={entry.id} className="bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0">
                    {entry.user.avatar ? (
                      <Image
                        src={entry.user.avatar}
                        alt={entry.user.name}
                        width={48}
                        height={48}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-lg font-serif text-gray-500">
                        {entry.user.name.charAt(0).toUpperCase()}
                      </span>
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-medium text-gray-900">{entry.user.name}</h3>
                      <span className="text-gray-400">•</span>
                      <span className="text-gray-500 text-sm flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        {formatDate(entry.created_at)}
                      </span>
                      
                      {entry.is_free ? (
                        <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-medium">
                          FREE
                        </span>
                      ) : (
                        <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                          <CreditCard className="w-3 h-3" />
                          {entry.credits_required} credit{entry.credits_required > 1 ? 's' : ''}
                        </span>
                      )}
                      
                      {entry.bundle_count > 1 && (
                        <span className="bg-purple-100 text-purple-700 px-2 py-1 rounded-full text-xs font-medium">
                          Bundle of {entry.bundle_count}
                        </span>
                      )}
                    </div>
                    
                    <h4 className="text-lg font-serif text-gray-800 mb-3">{entry.title}</h4>
                    
                    <p className="text-gray-600 leading-relaxed mb-4">
                      {entry.body_md.slice(0, 200)}...
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <Heart className="w-4 h-4" />
                          <span>{entry.loves_count || 0}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <MessageCircle className="w-4 h-4" />
                          <span>{entry.comments_count || 0}</span>
                        </div>
                      </div>
                      
                      <button
                        onClick={() => handleReadEntry(entry)}
                        className={`px-4 py-2 rounded-xl font-medium transition-colors ${
                          entry.is_free || entry.has_access
                            ? 'bg-blue-600 text-white hover:bg-blue-700'
                            : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                        }`}
                      >
                        {entry.is_free ? 'Read Free' : 
                         entry.has_access ? 'Read Now' : 
                         `Use ${entry.credits_required} Credit${entry.credits_required > 1 ? 's' : ''}`}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-2xl p-12 text-center shadow-sm">
            <div className="text-6xl mb-4">📚</div>
            <h3 className="text-xl font-serif text-gray-800 mb-2">No Stories Yet</h3>
            <p className="text-gray-600 mb-6">
              Start following creators to see their latest stories here
            </p>
            <Link
              href="/discover"
              className="inline-block bg-blue-600 text-white px-6 py-3 rounded-xl font-medium hover:bg-blue-700 transition-colors"
            >
              Discover Creators
            </Link>
          </div>
        )}
      </div>
    </div>
  )
}
