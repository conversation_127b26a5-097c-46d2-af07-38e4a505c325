"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { createSupabaseClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export default function UploadEbookPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    genre: '',
    keywords: '',
    priceAmount: '',
    isbn: '',
    publicationDate: '',
    metaDescription: '',
    slug: ''
  })
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [coverFile, setCoverFile] = useState<File | null>(null)
  const [coverPreview, setCoverPreview] = useState<string | null>(null)
  const [uploading, setUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const [coverDragActive, setCoverDragActive] = useState(false)
  const supabase = createSupabaseClient()

  // Comprehensive genre list like Amazon
  const genres = [
    'Action & Adventure', 'Alternate History', 'Anthology', 'Biographies & Memoirs',
    'Business & Money', 'Children\'s Books', 'Christian Fiction', 'Comics & Graphic Novels',
    'Contemporary Fiction', 'Cookbooks', 'Crime & Mystery', 'Dystopian', 'Education & Teaching',
    'Epic Fantasy', 'Erotica', 'Fantasy', 'Health & Fitness', 'Historical Fiction',
    'History', 'Horror', 'Humor & Entertainment', 'LGBTQ+', 'Literary Fiction',
    'Medical Thrillers', 'Memoirs', 'Military Fiction', 'Music', 'Mystery',
    'New Adult', 'Non-Fiction', 'Paranormal Romance', 'Parenting & Relationships',
    'Philosophy', 'Poetry', 'Political Thrillers', 'Politics & Social Sciences',
    'Psychology', 'Religion & Spirituality', 'Romance', 'Science & Math',
    'Science Fiction', 'Self-Help', 'Short Stories', 'Sports & Outdoors',
    'Suspense', 'Technology', 'Teen & Young Adult', 'Thrillers', 'Travel',
    'True Crime', 'Urban Fantasy', 'War & Military', 'Western', 'Women\'s Fiction'
  ]

  // Calculate earnings breakdown
  const calculateEarnings = (price: string) => {
    const priceNum = parseFloat(price) || 0
    if (priceNum === 0) return { gross: 0, stripeFee: 0, platformFee: 0, authorEarnings: 0 }

    const grossCents = Math.round(priceNum * 100)
    const stripeFee = Math.round(grossCents * 0.029 + 30) // 2.9% + 30¢
    const afterStripe = grossCents - stripeFee
    const platformFee = Math.round(afterStripe * 0.20) // 20% platform fee
    const authorEarnings = afterStripe - platformFee

    return {
      gross: grossCents / 100,
      stripeFee: stripeFee / 100,
      platformFee: platformFee / 100,
      authorEarnings: authorEarnings / 100
    }
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
      // Auto-generate slug from title
      ...(field === 'title' && { slug: generateSlug(value) })
    }))
  }

  const handleFileSelect = (file: File) => {
    // Validate file type
    const allowedTypes = ['application/pdf', 'application/epub+zip']
    if (!allowedTypes.includes(file.type)) {
      alert('Please select a PDF or EPUB file')
      return
    }

    // Validate file size (max 50MB)
    if (file.size > 50 * 1024 * 1024) {
      alert('File size must be less than 50MB')
      return
    }

    setSelectedFile(file)
  }

  const handleCoverSelect = (file: File) => {
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file for the cover')
      return
    }

    if (file.size > 5 * 1024 * 1024) {
      alert('Cover image must be less than 5MB')
      return
    }

    setCoverFile(file)

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      setCoverPreview(e.target?.result as string)
    }
    reader.readAsDataURL(file)
  }

  const handleCoverDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setCoverDragActive(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleCoverSelect(files[0])
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.title.trim() || !selectedFile || !coverFile) {
      alert('Please provide a title, select an ebook file, and upload a book cover')
      return
    }

    setUploading(true)
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/auth/login')
        return
      }

      // Upload ebook file
      const fileExt = selectedFile.name.split('.').pop()
      const fileName = `${Date.now()}-${formData.slug}.${fileExt}`
      
      const { data: fileUpload, error: fileError } = await supabase.storage
        .from('ebooks')
        .upload(fileName, selectedFile)

      if (fileError) throw fileError

      const { data: { publicUrl: ebookUrl } } = supabase.storage
        .from('ebooks')
        .getPublicUrl(fileName)

      // Upload cover (now mandatory)
      const coverExt = coverFile.name.split('.').pop()
      const coverFileName = `${Date.now()}-${formData.slug}-cover.${coverExt}`

      const { data: coverUpload, error: coverError } = await supabase.storage
        .from('project-covers')
        .upload(coverFileName, coverFile)

      if (coverError) throw coverError

      const { data: { publicUrl: coverUrl } } = supabase.storage
        .from('project-covers')
        .getPublicUrl(coverFileName)

      // Create project record
      const priceInCents = formData.priceAmount ? Math.round(parseFloat(formData.priceAmount) * 100) : 0
      const keywordsArray = formData.keywords ? formData.keywords.split(',').map(keyword => keyword.trim()).filter(Boolean) : []

      const { data: project, error: projectError } = await supabase
        .from('projects')
        .insert({
          user_id: user.id,
          title: formData.title.trim(),
          description: formData.description.trim() || null,
          genre: formData.genre.trim() || null,
          tags: keywordsArray,
          price_amount: priceInCents,
          preview_chapters: 1, // Only allow chapter 1
          isbn: formData.isbn.trim() || null,
          publication_date: formData.publicationDate || null,
          meta_description: formData.metaDescription.trim() || null,
          slug: formData.slug.trim(),
          is_ebook: true,
          is_complete: true,
          ebook_file_url: ebookUrl,
          ebook_file_type: fileExt as 'pdf' | 'epub',
          cover_image_url: coverUrl,
          social_image_url: coverUrl // Use cover as social image
        })
        .select()
        .single()

      if (projectError) throw projectError

      router.push(`/books/${project.id}`)
    } catch (error) {
      console.error('Error uploading ebook:', error)
      alert('Failed to upload ebook. Please try again.')
    } finally {
      setUploading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-serif text-gray-900 mb-4">
            ✨ Publish Your Masterpiece
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Transform your completed book into a published work. Upload your ebook and cover to join our curated store where readers discover their next favorite read.
          </p>
          <div className="mt-4 inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium">
            💰 You keep 80% of all sales after platform fees
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">

          {/* Book Cover Upload - First and Mandatory */}
          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-serif text-gray-900 mb-4">📸 Book Cover *</h2>
              <p className="text-gray-600 mb-4">
                A professional book cover is mandatory for store listing. Recommended 10:16 aspect ratio for best results.
              </p>

              <div
                onDrop={handleCoverDrop}
                onDragOver={(e) => { e.preventDefault(); setCoverDragActive(true) }}
                onDragLeave={() => setCoverDragActive(false)}
                className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                  coverDragActive ? 'border-purple-400 bg-purple-50' : 'border-gray-300'
                }`}
              >
                {coverPreview ? (
                  <div className="flex flex-col items-center">
                    <img
                      src={coverPreview}
                      alt="Cover preview"
                      className="w-32 h-48 object-cover rounded-lg shadow-md mb-4"
                    />
                    <p className="font-medium text-gray-900 mb-2">{coverFile?.name}</p>
                    <p className="text-sm text-gray-500 mb-4">
                      {coverFile ? (coverFile.size / (1024 * 1024)).toFixed(2) : 0} MB
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setCoverFile(null)
                        setCoverPreview(null)
                      }}
                    >
                      Remove Cover
                    </Button>
                  </div>
                ) : (
                  <div>
                    <div className="text-4xl mb-2">📸</div>
                    <p className="text-gray-600 mb-2">
                      Drag & drop your book cover here
                    </p>
                    <p className="text-xs text-gray-500 mb-4">
                      Recommended: 10:16 aspect ratio • Max size: 5MB • JPG, PNG, WebP
                    </p>

                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) handleCoverSelect(file)
                      }}
                      className="hidden"
                      id="cover-upload"
                    />
                    <label
                      htmlFor="cover-upload"
                      className="bg-purple-600 text-white px-6 py-2 rounded-lg cursor-pointer hover:bg-purple-700 transition-colors inline-block"
                    >
                      Choose Cover Image
                    </label>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* File Upload Section */}
          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-serif text-gray-900 mb-4">📖 Book File *</h2>
              
              <div
                onDrop={handleDrop}
                onDragOver={(e) => { e.preventDefault(); setDragActive(true) }}
                onDragLeave={() => setDragActive(false)}
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  dragActive ? 'border-purple-400 bg-purple-50' : 'border-gray-300'
                }`}
              >
                {selectedFile ? (
                  <div>
                    <div className="text-4xl mb-2">📄</div>
                    <p className="font-medium text-gray-900">{selectedFile.name}</p>
                    <p className="text-sm text-gray-500">
                      {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setSelectedFile(null)}
                      className="mt-2"
                    >
                      Remove File
                    </Button>
                  </div>
                ) : (
                  <div>
                    <div className="text-4xl mb-2">📚</div>
                    <p className="text-gray-600 mb-2">
                      Drag & drop your ebook file here
                    </p>
                    <p className="text-xs text-gray-500 mb-4">
                      Supported formats: PDF, EPUB • Max size: 50MB
                    </p>
                    
                    <input
                      type="file"
                      accept=".pdf,.epub"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) handleFileSelect(file)
                      }}
                      className="hidden"
                      id="ebook-upload"
                    />
                    <label
                      htmlFor="ebook-upload"
                      className="bg-purple-600 text-white px-6 py-2 rounded-lg cursor-pointer hover:bg-purple-700 transition-colors inline-block"
                    >
                      Choose File
                    </label>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Book Details */}
          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-serif text-gray-900 mb-4">📝 Book Details</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Book Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Enter your book title"
                    required
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Book Description *
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 h-24 resize-none"
                    placeholder="Write a compelling description for your book..."
                    maxLength={1000}
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {formData.description.length}/1000 characters
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Genre *
                  </label>
                  <select
                    value={formData.genre}
                    onChange={(e) => handleInputChange('genre', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                    required
                  >
                    <option value="">Select genre</option>
                    {genres.map((genre) => (
                      <option key={genre} value={genre}>{genre}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Keywords
                  </label>
                  <input
                    type="text"
                    value={formData.keywords}
                    onChange={(e) => handleInputChange('keywords', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="romance, love story, contemporary (comma-separated)"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Help readers discover your book with relevant keywords
                  </p>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Price (USD)
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-2 text-gray-500">$</span>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.priceAmount}
                      onChange={(e) => handleInputChange('priceAmount', e.target.value)}
                      className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                      placeholder="0.00"
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Leave empty or set to 0 for free ebook
                  </p>

                  {/* Earnings Calculator */}
                  {formData.priceAmount && parseFloat(formData.priceAmount) > 0 && (
                    <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                      <h4 className="font-medium text-green-900 mb-2">💰 Your Earnings Breakdown</h4>
                      {(() => {
                        const earnings = calculateEarnings(formData.priceAmount)
                        return (
                          <div className="space-y-1 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Book Price:</span>
                              <span className="font-medium">${earnings.gross.toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Stripe Fee (2.9% + 30¢):</span>
                              <span className="text-red-600">-${earnings.stripeFee.toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">OnlyDiary Fee (20%):</span>
                              <span className="text-red-600">-${earnings.platformFee.toFixed(2)}</span>
                            </div>
                            <div className="border-t border-green-300 pt-1 mt-2">
                              <div className="flex justify-between font-bold">
                                <span className="text-green-900">You Keep (80% after fees):</span>
                                <span className="text-green-900">${earnings.authorEarnings.toFixed(2)}</span>
                              </div>
                            </div>
                          </div>
                        )
                      })()}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Additional Metadata */}
          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-serif text-gray-900 mb-4">📋 Additional Information</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ISBN (Optional)
                  </label>
                  <input
                    type="text"
                    value={formData.isbn}
                    onChange={(e) => handleInputChange('isbn', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="978-0-123456-78-9"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    International Standard Book Number
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Publication Date
                  </label>
                  <input
                    type="date"
                    value={formData.publicationDate}
                    onChange={(e) => handleInputChange('publicationDate', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    SEO Description (Optional)
                  </label>
                  <textarea
                    value={formData.metaDescription}
                    onChange={(e) => handleInputChange('metaDescription', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 h-20 resize-none"
                    placeholder="A brief description for search engines and social media..."
                    maxLength={160}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {formData.metaDescription.length}/160 characters • Helps with discoverability
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex gap-4 justify-end">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={uploading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              isLoading={uploading}
              disabled={!formData.title.trim() || !selectedFile || !coverFile}
              className="bg-purple-600 text-white hover:bg-purple-700"
            >
              {uploading ? 'Uploading...' : 'Publish to Store'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
