"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { createSupabaseClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export default function UploadEbookPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    genre: '',
    bookType: '',
    tags: '',
    priceAmount: '',
    previewChapters: '1',
    isbn: '',
    publicationDate: '',
    metaDescription: '',
    slug: ''
  })
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [coverFile, setCoverFile] = useState<File | null>(null)
  const [uploading, setUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const supabase = createSupabaseClient()

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
      // Auto-generate slug from title
      ...(field === 'title' && { slug: generateSlug(value) })
    }))
  }

  const handleFileSelect = (file: File) => {
    // Validate file type
    const allowedTypes = ['application/pdf', 'application/epub+zip']
    if (!allowedTypes.includes(file.type)) {
      alert('Please select a PDF or EPUB file')
      return
    }

    // Validate file size (max 50MB)
    if (file.size > 50 * 1024 * 1024) {
      alert('File size must be less than 50MB')
      return
    }

    setSelectedFile(file)
  }

  const handleCoverSelect = (file: File) => {
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file for the cover')
      return
    }

    if (file.size > 5 * 1024 * 1024) {
      alert('Cover image must be less than 5MB')
      return
    }

    setCoverFile(file)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim() || !selectedFile) {
      alert('Please provide a title and select an ebook file')
      return
    }

    setUploading(true)
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/auth/login')
        return
      }

      // Upload ebook file
      const fileExt = selectedFile.name.split('.').pop()
      const fileName = `${Date.now()}-${formData.slug}.${fileExt}`
      
      const { data: fileUpload, error: fileError } = await supabase.storage
        .from('ebooks')
        .upload(fileName, selectedFile)

      if (fileError) throw fileError

      const { data: { publicUrl: ebookUrl } } = supabase.storage
        .from('ebooks')
        .getPublicUrl(fileName)

      // Upload cover if provided
      let coverUrl = null
      if (coverFile) {
        const coverExt = coverFile.name.split('.').pop()
        const coverFileName = `${Date.now()}-${formData.slug}-cover.${coverExt}`
        
        const { data: coverUpload, error: coverError } = await supabase.storage
          .from('project-covers')
          .upload(coverFileName, coverFile)

        if (coverError) throw coverError

        const { data: { publicUrl } } = supabase.storage
          .from('project-covers')
          .getPublicUrl(coverFileName)
        
        coverUrl = publicUrl
      }

      // Create project record
      const priceInCents = formData.priceAmount ? Math.round(parseFloat(formData.priceAmount) * 100) : 0
      const tagsArray = formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(Boolean) : []

      const { data: project, error: projectError } = await supabase
        .from('projects')
        .insert({
          user_id: user.id,
          title: formData.title.trim(),
          description: formData.description.trim() || null,
          genre: formData.genre.trim() || null,
          book_type: formData.bookType.trim() || null,
          tags: tagsArray,
          price_amount: priceInCents,
          preview_chapters: parseInt(formData.previewChapters) || 1,
          isbn: formData.isbn.trim() || null,
          publication_date: formData.publicationDate || null,
          meta_description: formData.metaDescription.trim() || null,
          slug: formData.slug.trim(),
          is_ebook: true,
          is_complete: true,
          ebook_file_url: ebookUrl,
          ebook_file_type: fileExt as 'pdf' | 'epub',
          cover_image_url: coverUrl,
          social_image_url: coverUrl // Use cover as social image
        })
        .select()
        .single()

      if (projectError) throw projectError

      router.push(`/books/${project.id}`)
    } catch (error) {
      console.error('Error uploading ebook:', error)
      alert('Failed to upload ebook. Please try again.')
    } finally {
      setUploading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-serif text-gray-900 mb-4">
            📚 Upload Your Ebook
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Share your completed book with OnlyDiary readers. Upload your PDF or EPUB file and set up your book store page.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          
          {/* File Upload Section */}
          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-serif text-gray-900 mb-4">📖 Book File</h2>
              
              <div
                onDrop={handleDrop}
                onDragOver={(e) => { e.preventDefault(); setDragActive(true) }}
                onDragLeave={() => setDragActive(false)}
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  dragActive ? 'border-purple-400 bg-purple-50' : 'border-gray-300'
                }`}
              >
                {selectedFile ? (
                  <div>
                    <div className="text-4xl mb-2">📄</div>
                    <p className="font-medium text-gray-900">{selectedFile.name}</p>
                    <p className="text-sm text-gray-500">
                      {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setSelectedFile(null)}
                      className="mt-2"
                    >
                      Remove File
                    </Button>
                  </div>
                ) : (
                  <div>
                    <div className="text-4xl mb-2">📚</div>
                    <p className="text-gray-600 mb-2">
                      Drag & drop your ebook file here
                    </p>
                    <p className="text-xs text-gray-500 mb-4">
                      Supported formats: PDF, EPUB • Max size: 50MB
                    </p>
                    
                    <input
                      type="file"
                      accept=".pdf,.epub"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) handleFileSelect(file)
                      }}
                      className="hidden"
                      id="ebook-upload"
                    />
                    <label
                      htmlFor="ebook-upload"
                      className="bg-purple-600 text-white px-6 py-2 rounded-lg cursor-pointer hover:bg-purple-700 transition-colors inline-block"
                    >
                      Choose File
                    </label>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Book Details */}
          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-serif text-gray-900 mb-4">📝 Book Details</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Book Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Enter your book title"
                    required
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Book Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 h-24 resize-none"
                    placeholder="Write a compelling description for your book..."
                    maxLength={1000}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {formData.description.length}/1000 characters
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Genre
                  </label>
                  <select
                    value={formData.genre}
                    onChange={(e) => handleInputChange('genre', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="">Select genre</option>
                    <option value="fiction">Fiction</option>
                    <option value="non_fiction">Non-Fiction</option>
                    <option value="romance">Romance</option>
                    <option value="mystery">Mystery</option>
                    <option value="sci_fi">Science Fiction</option>
                    <option value="fantasy">Fantasy</option>
                    <option value="biography">Biography</option>
                    <option value="self_help">Self Help</option>
                    <option value="poetry">Poetry</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Book Type
                  </label>
                  <select
                    value={formData.bookType}
                    onChange={(e) => handleInputChange('bookType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="">Select type</option>
                    <option value="novel">Novel</option>
                    <option value="short_story">Short Story</option>
                    <option value="collection">Collection</option>
                    <option value="memoir">Memoir</option>
                    <option value="guide">Guide</option>
                    <option value="textbook">Textbook</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Price (USD)
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-2 text-gray-500">$</span>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.priceAmount}
                      onChange={(e) => handleInputChange('priceAmount', e.target.value)}
                      className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                      placeholder="0.00"
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Leave empty or set to 0 for free ebook
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Preview Chapters
                  </label>
                  <select
                    value={formData.previewChapters}
                    onChange={(e) => handleInputChange('previewChapters', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="1">1 chapter</option>
                    <option value="2">2 chapters</option>
                    <option value="3">3 chapters</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex gap-4 justify-end">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={uploading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              isLoading={uploading}
              disabled={!formData.title.trim() || !selectedFile}
              className="bg-purple-600 text-white hover:bg-purple-700"
            >
              {uploading ? 'Uploading...' : 'Upload Ebook'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
