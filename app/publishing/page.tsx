"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { BookManager } from "@/components/BookManager"

interface Book {
  id: string
  title: string
  description: string
  cover_image_url: string
  genre: string
  book_type: string
  price_amount: number
  average_rating: number
  review_count: number
  sales_count: number
  is_ebook: boolean
  is_complete: boolean
  is_private: boolean
  created_at: string
  updated_at: string
}

interface User {
  id: string
  name: string
  role: string
}

export default function PublishingCenter() {
  const [user, setUser] = useState<User | null>(null)
  const [books, setBooks] = useState<Book[]>([])
  const [loading, setLoading] = useState(true)
  const [totalEarnings, setTotalEarnings] = useState(0)
  const [monthlyEarnings, setMonthlyEarnings] = useState(0)
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    checkAuthAndFetchData()
  }, [])

  const checkAuthAndFetchData = async () => {
    try {
      const { data: { user: authUser }, error } = await supabase.auth.getUser()
      
      if (error || !authUser) {
        router.push('/login')
        return
      }

      // Get user profile
      const { data: profile } = await supabase
        .from("users")
        .select("*")
        .eq("id", authUser.id)
        .single()

      if (!profile || (profile.role !== 'writer' && profile.role !== 'admin')) {
        router.push('/')
        return
      }

      setUser(profile)
      await fetchBooks(authUser.id)
      await fetchEarnings(authUser.id)
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchBooks = async (userId: string) => {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false })

    if (!error && data) {
      setBooks(data)
    }
  }

  const fetchEarnings = async (userId: string) => {
    // Calculate earnings from book sales (80% royalty)
    const { data: purchases } = await supabase
      .from('book_purchases')
      .select(`
        amount_paid,
        created_at,
        projects!inner(user_id)
      `)
      .eq('projects.user_id', userId)
      .eq('status', 'completed')

    if (purchases) {
      const total = purchases.reduce((sum, purchase) => sum + (purchase.amount_paid * 0.8), 0)
      setTotalEarnings(total)

      // Calculate this month's earnings
      const thisMonth = new Date()
      thisMonth.setDate(1)
      const monthlyPurchases = purchases.filter(p => new Date(p.created_at) >= thisMonth)
      const monthly = monthlyPurchases.reduce((sum, purchase) => sum + (purchase.amount_paid * 0.8), 0)
      setMonthlyEarnings(monthly)
    }
  }

  const formatPrice = (cents: number) => {
    if (cents === 0) return "Free"
    return `$${(cents / 100).toFixed(2)}`
  }

  const formatEarnings = (cents: number) => {
    return `$${(cents / 100).toFixed(2)}`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading Publishing Center...</p>
        </div>
      </div>
    )
  }

  const publishedBooks = books.filter(book => book.is_ebook && book.is_complete)
  const draftBooks = books.filter(book => !book.is_ebook || !book.is_complete)

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            📚 Publishing Center
          </h1>
          <p className="text-gray-600">
            Manage your books, track sales, and grow your author business
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <span className="text-2xl">💰</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Earnings</p>
                <p className="text-2xl font-bold text-gray-900">{formatEarnings(totalEarnings)}</p>
                <p className="text-xs text-green-600">80% royalty rate*</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <span className="text-2xl">📈</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">This Month</p>
                <p className="text-2xl font-bold text-gray-900">{formatEarnings(monthlyEarnings)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <span className="text-2xl">📚</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Published Books</p>
                <p className="text-2xl font-bold text-gray-900">{publishedBooks.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <span className="text-2xl">✏️</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Drafts</p>
                <p className="text-2xl font-bold text-gray-900">{draftBooks.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link href="/write/upload-ebook">
              <Button className="w-full bg-purple-600 text-white hover:bg-purple-700">
                📖 Publish New Book
              </Button>
            </Link>
            <Link href="/write/projects">
              <Button variant="outline" className="w-full">
                ✏️ Start Writing Project
              </Button>
            </Link>
            <Link href="/books">
              <Button variant="outline" className="w-full">
                🛍️ View Book Store
              </Button>
            </Link>
          </div>
        </div>

        {/* Published Books */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-900">Published Books</h2>
              <Link href="/write/upload-ebook">
                <Button size="sm" className="bg-purple-600 text-white hover:bg-purple-700">
                  + Publish Book
                </Button>
              </Link>
            </div>
          </div>
          
          <div className="p-6">
            {publishedBooks.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">📚</span>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No published books yet</h3>
                <p className="text-gray-600 mb-6">Start earning by publishing your first book</p>
                <Link href="/write/upload-ebook">
                  <Button className="bg-purple-600 text-white hover:bg-purple-700">
                    Publish Your First Book
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-6">
                {publishedBooks.map((book) => (
                  <BookManager
                    key={book.id}
                    book={book}
                    onUpdate={() => fetchBooks(user!.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Draft Books */}
        {draftBooks.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-bold text-gray-900">Draft Books & Projects</h2>
              <p className="text-gray-600 text-sm mt-1">Complete these to publish in your store</p>
            </div>

            <div className="p-6">
              <div className="space-y-6">
                {draftBooks.map((book) => (
                  <BookManager
                    key={book.id}
                    book={book}
                    onUpdate={() => fetchBooks(user!.id)}
                  />
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Footnote */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <p className="text-xs text-gray-500 text-center">
            * 80% royalty rate applies after Stripe processing fees (2.9% + 30¢ per transaction)
          </p>
        </div>

      </div>
    </div>
  )
}
