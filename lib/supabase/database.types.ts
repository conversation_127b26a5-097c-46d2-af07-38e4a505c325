Need to install the following packages:
supabase@2.26.9
Ok to proceed? (y) 
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      blocks: {
        Row: {
          blocked_id: string | null
          blocker_id: string | null
          created_at: string | null
          id: string
        }
        Insert: {
          blocked_id?: string | null
          blocker_id?: string | null
          created_at?: string | null
          id?: string
        }
        Update: {
          blocked_id?: string | null
          blocker_id?: string | null
          created_at?: string | null
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "blocks_blocked_id_fkey"
            columns: ["blocked_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "blocks_blocker_id_fkey"
            columns: ["blocker_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      bookmarks: {
        Row: {
          created_at: string | null
          creator_id: string
          id: string
          reader_id: string
        }
        Insert: {
          created_at?: string | null
          creator_id: string
          id?: string
          reader_id: string
        }
        Update: {
          created_at?: string | null
          creator_id?: string
          id?: string
          reader_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "bookmarks_creator_id_fkey"
            columns: ["creator_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookmarks_reader_id_fkey"
            columns: ["reader_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      chapter_comments: {
        Row: {
          chapter_id: string | null
          content: string
          created_at: string | null
          id: string
          user_id: string | null
        }
        Insert: {
          chapter_id?: string | null
          content: string
          created_at?: string | null
          id?: string
          user_id?: string | null
        }
        Update: {
          chapter_id?: string | null
          content?: string
          created_at?: string | null
          id?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "chapter_comments_chapter_id_fkey"
            columns: ["chapter_id"]
            isOneToOne: false
            referencedRelation: "chapters"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chapter_comments_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      chapter_loves: {
        Row: {
          chapter_id: string | null
          created_at: string | null
          id: string
          user_id: string | null
        }
        Insert: {
          chapter_id?: string | null
          created_at?: string | null
          id?: string
          user_id?: string | null
        }
        Update: {
          chapter_id?: string | null
          created_at?: string | null
          id?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "chapter_loves_chapter_id_fkey"
            columns: ["chapter_id"]
            isOneToOne: false
            referencedRelation: "chapters"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chapter_loves_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      chapters: {
        Row: {
          allow_donations: boolean | null
          chapter_number: number
          chapter_price: number | null
          content: string
          created_at: string | null
          id: string
          is_free: boolean | null
          is_published: boolean | null
          love_count: number | null
          project_id: string | null
          title: string
          updated_at: string | null
          user_id: string | null
          word_count: number | null
        }
        Insert: {
          allow_donations?: boolean | null
          chapter_number: number
          chapter_price?: number | null
          content: string
          created_at?: string | null
          id?: string
          is_free?: boolean | null
          is_published?: boolean | null
          love_count?: number | null
          project_id?: string | null
          title: string
          updated_at?: string | null
          user_id?: string | null
          word_count?: number | null
        }
        Update: {
          allow_donations?: boolean | null
          chapter_number?: number
          chapter_price?: number | null
          content?: string
          created_at?: string | null
          id?: string
          is_free?: boolean | null
          is_published?: boolean | null
          love_count?: number | null
          project_id?: string | null
          title?: string
          updated_at?: string | null
          user_id?: string | null
          word_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "chapters_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chapters_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      comments: {
        Row: {
          body: string
          created_at: string | null
          diary_entry_id: string | null
          id: string
          is_deleted: boolean | null
          parent_comment_id: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          body: string
          created_at?: string | null
          diary_entry_id?: string | null
          id?: string
          is_deleted?: boolean | null
          parent_comment_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          body?: string
          created_at?: string | null
          diary_entry_id?: string | null
          id?: string
          is_deleted?: boolean | null
          parent_comment_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "comments_diary_entry_id_fkey"
            columns: ["diary_entry_id"]
            isOneToOne: false
            referencedRelation: "diary_entries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_parent_comment_id_fkey"
            columns: ["parent_comment_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      diary_entries: {
        Row: {
          allow_donations: boolean | null
          body_md: string
          bundle_count: number | null
          created_at: string | null
          id: string
          is_free: boolean | null
          is_hidden: boolean | null
          keywords: string[] | null
          love_count: number | null
          title: string
          updated_at: string | null
          user_id: string | null
          view_count: number | null
        }
        Insert: {
          allow_donations?: boolean | null
          body_md: string
          bundle_count?: number | null
          created_at?: string | null
          id?: string
          is_free?: boolean | null
          is_hidden?: boolean | null
          keywords?: string[] | null
          love_count?: number | null
          title: string
          updated_at?: string | null
          user_id?: string | null
          view_count?: number | null
        }
        Update: {
          allow_donations?: boolean | null
          body_md?: string
          bundle_count?: number | null
          created_at?: string | null
          id?: string
          is_free?: boolean | null
          is_hidden?: boolean | null
          keywords?: string[] | null
          love_count?: number | null
          title?: string
          updated_at?: string | null
          user_id?: string | null
          view_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "diary_entries_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      donations: {
        Row: {
          amount: number
          chapter_id: string | null
          created_at: string | null
          diary_entry_id: string | null
          donor_id: string | null
          id: string
          is_anonymous: boolean | null
          message: string | null
          payment_id: string | null
          project_id: string | null
          recipient_id: string | null
          stripe_payment_id: string | null
          target_type: string | null
        }
        Insert: {
          amount: number
          chapter_id?: string | null
          created_at?: string | null
          diary_entry_id?: string | null
          donor_id?: string | null
          id?: string
          is_anonymous?: boolean | null
          message?: string | null
          payment_id?: string | null
          project_id?: string | null
          recipient_id?: string | null
          stripe_payment_id?: string | null
          target_type?: string | null
        }
        Update: {
          amount?: number
          chapter_id?: string | null
          created_at?: string | null
          diary_entry_id?: string | null
          donor_id?: string | null
          id?: string
          is_anonymous?: boolean | null
          message?: string | null
          payment_id?: string | null
          project_id?: string | null
          recipient_id?: string | null
          stripe_payment_id?: string | null
          target_type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "donations_chapter_id_fkey"
            columns: ["chapter_id"]
            isOneToOne: false
            referencedRelation: "chapters"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "donations_diary_entry_id_fkey"
            columns: ["diary_entry_id"]
            isOneToOne: false
            referencedRelation: "diary_entries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "donations_donor_id_fkey"
            columns: ["donor_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "donations_payment_id_fkey"
            columns: ["payment_id"]
            isOneToOne: false
            referencedRelation: "payments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "donations_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "donations_writer_id_fkey"
            columns: ["recipient_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      favorite_creators: {
        Row: {
          created_at: string | null
          id: string
          user_id: string
          writer_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          user_id: string
          writer_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          user_id?: string
          writer_id?: string
        }
        Relationships: []
      }
      flags: {
        Row: {
          created_at: string | null
          diary_entry_id: string | null
          id: string
          reason: string
          reporter_id: string | null
          resolved: boolean | null
        }
        Insert: {
          created_at?: string | null
          diary_entry_id?: string | null
          id?: string
          reason: string
          reporter_id?: string | null
          resolved?: boolean | null
        }
        Update: {
          created_at?: string | null
          diary_entry_id?: string | null
          id?: string
          reason?: string
          reporter_id?: string | null
          resolved?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "flags_diary_entry_id_fkey"
            columns: ["diary_entry_id"]
            isOneToOne: false
            referencedRelation: "diary_entries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "flags_reporter_id_fkey"
            columns: ["reporter_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      flowers: {
        Row: {
          created_at: string | null
          giver_id: string
          id: string
          message: string
          receiver_id: string
        }
        Insert: {
          created_at?: string | null
          giver_id: string
          id?: string
          message: string
          receiver_id: string
        }
        Update: {
          created_at?: string | null
          giver_id?: string
          id?: string
          message?: string
          receiver_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "flowers_giver_id_fkey"
            columns: ["giver_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "flowers_receiver_id_fkey"
            columns: ["receiver_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      follows: {
        Row: {
          created_at: string | null
          follower_id: string
          id: string
          writer_id: string
        }
        Insert: {
          created_at?: string | null
          follower_id: string
          id?: string
          writer_id: string
        }
        Update: {
          created_at?: string | null
          follower_id?: string
          id?: string
          writer_id?: string
        }
        Relationships: []
      }
      hourly_love_stats: {
        Row: {
          created_at: string | null
          diary_entry_id: string | null
          hour_start: string | null
          id: string
          love_count: number | null
        }
        Insert: {
          created_at?: string | null
          diary_entry_id?: string | null
          hour_start?: string | null
          id?: string
          love_count?: number | null
        }
        Update: {
          created_at?: string | null
          diary_entry_id?: string | null
          hour_start?: string | null
          id?: string
          love_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "hourly_love_stats_diary_entry_id_fkey"
            columns: ["diary_entry_id"]
            isOneToOne: false
            referencedRelation: "diary_entries"
            referencedColumns: ["id"]
          },
        ]
      }
      invites: {
        Row: {
          contact_email: string | null
          contact_name: string | null
          contact_phone: string | null
          created_at: string | null
          id: string
          invite_code: string
          inviter_id: string | null
          method: string | null
          used_at: string | null
          used_by: string | null
        }
        Insert: {
          contact_email?: string | null
          contact_name?: string | null
          contact_phone?: string | null
          created_at?: string | null
          id?: string
          invite_code: string
          inviter_id?: string | null
          method?: string | null
          used_at?: string | null
          used_by?: string | null
        }
        Update: {
          contact_email?: string | null
          contact_name?: string | null
          contact_phone?: string | null
          created_at?: string | null
          id?: string
          invite_code?: string
          inviter_id?: string | null
          method?: string | null
          used_at?: string | null
          used_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "invites_inviter_id_fkey"
            columns: ["inviter_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invites_used_by_fkey"
            columns: ["used_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      loves: {
        Row: {
          created_at: string | null
          diary_entry_id: string | null
          id: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          diary_entry_id?: string | null
          id?: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          diary_entry_id?: string | null
          id?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "loves_diary_entry_id_fkey"
            columns: ["diary_entry_id"]
            isOneToOne: false
            referencedRelation: "diary_entries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "loves_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          body: string
          clicked_at: string | null
          created_at: string | null
          data: Json | null
          id: string
          read_at: string | null
          title: string
          type: string
          user_id: string
        }
        Insert: {
          body: string
          clicked_at?: string | null
          created_at?: string | null
          data?: Json | null
          id?: string
          read_at?: string | null
          title: string
          type: string
          user_id: string
        }
        Update: {
          body?: string
          clicked_at?: string | null
          created_at?: string | null
          data?: Json | null
          id?: string
          read_at?: string | null
          title?: string
          type?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      payments: {
        Row: {
          amount_cents: number
          created_at: string | null
          credits_purchased: number | null
          credits_used_for_entry_id: string | null
          id: string
          kind: Database["public"]["Enums"]["payment_kind"]
          payer_id: string | null
          stripe_payment_id: string | null
          writer_id: string | null
        }
        Insert: {
          amount_cents: number
          created_at?: string | null
          credits_purchased?: number | null
          credits_used_for_entry_id?: string | null
          id?: string
          kind: Database["public"]["Enums"]["payment_kind"]
          payer_id?: string | null
          stripe_payment_id?: string | null
          writer_id?: string | null
        }
        Update: {
          amount_cents?: number
          created_at?: string | null
          credits_purchased?: number | null
          credits_used_for_entry_id?: string | null
          id?: string
          kind?: Database["public"]["Enums"]["payment_kind"]
          payer_id?: string | null
          stripe_payment_id?: string | null
          writer_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payments_credits_used_for_entry_id_fkey"
            columns: ["credits_used_for_entry_id"]
            isOneToOne: false
            referencedRelation: "diary_entries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payments_payer_id_fkey"
            columns: ["payer_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payments_writer_id_fkey"
            columns: ["writer_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      photos: {
        Row: {
          alt_text: string
          created_at: string | null
          diary_entry_id: string | null
          id: string
          moderation_status:
            | Database["public"]["Enums"]["moderation_status"]
            | null
          rekognition_labels: Json | null
          url: string
        }
        Insert: {
          alt_text: string
          created_at?: string | null
          diary_entry_id?: string | null
          id?: string
          moderation_status?:
            | Database["public"]["Enums"]["moderation_status"]
            | null
          rekognition_labels?: Json | null
          url: string
        }
        Update: {
          alt_text?: string
          created_at?: string | null
          diary_entry_id?: string | null
          id?: string
          moderation_status?:
            | Database["public"]["Enums"]["moderation_status"]
            | null
          rekognition_labels?: Json | null
          url?: string
        }
        Relationships: [
          {
            foreignKeyName: "photos_diary_entry_id_fkey"
            columns: ["diary_entry_id"]
            isOneToOne: false
            referencedRelation: "diary_entries"
            referencedColumns: ["id"]
          },
        ]
      }
      post_credits: {
        Row: {
          created_at: string | null
          credits_remaining: number
          id: string
          last_purchase_date: string | null
          total_credits_purchased: number
          updated_at: string | null
          user_id: string | null
          writer_id: string | null
        }
        Insert: {
          created_at?: string | null
          credits_remaining?: number
          id?: string
          last_purchase_date?: string | null
          total_credits_purchased?: number
          updated_at?: string | null
          user_id?: string | null
          writer_id?: string | null
        }
        Update: {
          created_at?: string | null
          credits_remaining?: number
          id?: string
          last_purchase_date?: string | null
          total_credits_purchased?: number
          updated_at?: string | null
          user_id?: string | null
          writer_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "post_credits_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "post_credits_writer_id_fkey"
            columns: ["writer_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      post_reads: {
        Row: {
          diary_entry_id: string | null
          id: string
          read_at: string | null
          user_id: string | null
          writer_id: string | null
        }
        Insert: {
          diary_entry_id?: string | null
          id?: string
          read_at?: string | null
          user_id?: string | null
          writer_id?: string | null
        }
        Update: {
          diary_entry_id?: string | null
          id?: string
          read_at?: string | null
          user_id?: string | null
          writer_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "post_reads_diary_entry_id_fkey"
            columns: ["diary_entry_id"]
            isOneToOne: false
            referencedRelation: "diary_entries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "post_reads_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "post_reads_writer_id_fkey"
            columns: ["writer_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      project_purchases: {
        Row: {
          created_at: string | null
          id: string
          payment_id: string | null
          project_id: string | null
          user_id: string | null
          writer_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          payment_id?: string | null
          project_id?: string | null
          user_id?: string | null
          writer_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          payment_id?: string | null
          project_id?: string | null
          user_id?: string | null
          writer_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "project_purchases_payment_id_fkey"
            columns: ["payment_id"]
            isOneToOne: false
            referencedRelation: "payments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "project_purchases_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "project_purchases_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "project_purchases_writer_id_fkey"
            columns: ["writer_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      project_subscriptions: {
        Row: {
          chapters_remaining: number | null
          chapters_total: number | null
          created_at: string | null
          expires_at: string | null
          id: string
          payment_id: string | null
          project_id: string | null
          user_id: string | null
          writer_id: string | null
        }
        Insert: {
          chapters_remaining?: number | null
          chapters_total?: number | null
          created_at?: string | null
          expires_at?: string | null
          id?: string
          payment_id?: string | null
          project_id?: string | null
          user_id?: string | null
          writer_id?: string | null
        }
        Update: {
          chapters_remaining?: number | null
          chapters_total?: number | null
          created_at?: string | null
          expires_at?: string | null
          id?: string
          payment_id?: string | null
          project_id?: string | null
          user_id?: string | null
          writer_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "project_subscriptions_payment_id_fkey"
            columns: ["payment_id"]
            isOneToOne: false
            referencedRelation: "payments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "project_subscriptions_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "project_subscriptions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "project_subscriptions_writer_id_fkey"
            columns: ["writer_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      project_waitlist: {
        Row: {
          created_at: string | null
          id: string
          notification_sent: boolean | null
          project_id: string | null
          user_email: string
          user_id: string | null
          user_name: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          notification_sent?: boolean | null
          project_id?: string | null
          user_email: string
          user_id?: string | null
          user_name?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          notification_sent?: boolean | null
          project_id?: string | null
          user_email?: string
          user_id?: string | null
          user_name?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "project_waitlist_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "project_waitlist_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      projects: {
        Row: {
          allow_donations: boolean | null
          cover_image_url: string | null
          created_at: string | null
          default_chapter_price: number | null
          description: string | null
          genre: string | null
          id: string
          is_complete: boolean | null
          is_private: boolean | null
          price_amount: number | null
          price_type: string | null
          pricing_type: string | null
          project_price: number | null
          title: string
          total_chapters: number | null
          total_words: number | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          allow_donations?: boolean | null
          cover_image_url?: string | null
          created_at?: string | null
          default_chapter_price?: number | null
          description?: string | null
          genre?: string | null
          id?: string
          is_complete?: boolean | null
          is_private?: boolean | null
          price_amount?: number | null
          price_type?: string | null
          pricing_type?: string | null
          project_price?: number | null
          title: string
          total_chapters?: number | null
          total_words?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          allow_donations?: boolean | null
          cover_image_url?: string | null
          created_at?: string | null
          default_chapter_price?: number | null
          description?: string | null
          genre?: string | null
          id?: string
          is_complete?: boolean | null
          is_private?: boolean | null
          price_amount?: number | null
          price_type?: string | null
          pricing_type?: string | null
          project_price?: number | null
          title?: string
          total_chapters?: number | null
          total_words?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "projects_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      purchases: {
        Row: {
          amount_cents: number
          buyer_id: string | null
          chapter_id: string | null
          created_at: string | null
          id: string
          project_id: string | null
          purchase_type: string
          seller_id: string | null
          stripe_payment_id: string | null
        }
        Insert: {
          amount_cents: number
          buyer_id?: string | null
          chapter_id?: string | null
          created_at?: string | null
          id?: string
          project_id?: string | null
          purchase_type: string
          seller_id?: string | null
          stripe_payment_id?: string | null
        }
        Update: {
          amount_cents?: number
          buyer_id?: string | null
          chapter_id?: string | null
          created_at?: string | null
          id?: string
          project_id?: string | null
          purchase_type?: string
          seller_id?: string | null
          stripe_payment_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "purchases_buyer_id_fkey"
            columns: ["buyer_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchases_chapter_id_fkey"
            columns: ["chapter_id"]
            isOneToOne: false
            referencedRelation: "chapters"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchases_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchases_seller_id_fkey"
            columns: ["seller_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      push_subscriptions: {
        Row: {
          created_at: string | null
          id: string
          subscription: Json
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          subscription: Json
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          subscription?: Json
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "push_subscriptions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      settings: {
        Row: {
          created_at: string | null
          id: string
          key: string
          updated_at: string | null
          value: Json
        }
        Insert: {
          created_at?: string | null
          id?: string
          key: string
          updated_at?: string | null
          value: Json
        }
        Update: {
          created_at?: string | null
          id?: string
          key?: string
          updated_at?: string | null
          value?: Json
        }
        Relationships: []
      }
      subscriber_favorites: {
        Row: {
          created_at: string | null
          id: string
          subscriber_id: string
          writer_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          subscriber_id: string
          writer_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          subscriber_id?: string
          writer_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscriber_favorites_subscriber_id_fkey"
            columns: ["subscriber_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriber_favorites_writer_id_fkey"
            columns: ["writer_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      subscriptions: {
        Row: {
          created_at: string | null
          current_period_end: string | null
          current_period_start: string | null
          id: string
          reader_id: string
          status: string
          stripe_subscription_id: string | null
          updated_at: string | null
          writer_id: string
        }
        Insert: {
          created_at?: string | null
          current_period_end?: string | null
          current_period_start?: string | null
          id?: string
          reader_id: string
          status?: string
          stripe_subscription_id?: string | null
          updated_at?: string | null
          writer_id: string
        }
        Update: {
          created_at?: string | null
          current_period_end?: string | null
          current_period_start?: string | null
          id?: string
          reader_id?: string
          status?: string
          stripe_subscription_id?: string | null
          updated_at?: string | null
          writer_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_reader_id_fkey"
            columns: ["reader_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_writer_id_fkey"
            columns: ["writer_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          accepts_donations: boolean | null
          avatar: string | null
          bio: string | null
          bookmark_count: number | null
          created_at: string | null
          custom_url: string | null
          email: string
          entry_count: number | null
          flower_count: number | null
          hide_subscriber_count: boolean | null
          id: string
          monetization_type: string | null
          name: string | null
          price_monthly: number | null
          profile_picture_url: string | null
          role: Database["public"]["Enums"]["user_role"] | null
          social_instagram: string | null
          social_twitter: string | null
          social_website: string | null
          stripe_account_id: string | null
          stripe_onboarding_complete: boolean | null
          subscriber_count: number | null
          updated_at: string | null
        }
        Insert: {
          accepts_donations?: boolean | null
          avatar?: string | null
          bio?: string | null
          bookmark_count?: number | null
          created_at?: string | null
          custom_url?: string | null
          email: string
          entry_count?: number | null
          flower_count?: number | null
          hide_subscriber_count?: boolean | null
          id?: string
          monetization_type?: string | null
          name?: string | null
          price_monthly?: number | null
          profile_picture_url?: string | null
          role?: Database["public"]["Enums"]["user_role"] | null
          social_instagram?: string | null
          social_twitter?: string | null
          social_website?: string | null
          stripe_account_id?: string | null
          stripe_onboarding_complete?: boolean | null
          subscriber_count?: number | null
          updated_at?: string | null
        }
        Update: {
          accepts_donations?: boolean | null
          avatar?: string | null
          bio?: string | null
          bookmark_count?: number | null
          created_at?: string | null
          custom_url?: string | null
          email?: string
          entry_count?: number | null
          flower_count?: number | null
          hide_subscriber_count?: boolean | null
          id?: string
          monetization_type?: string | null
          name?: string | null
          price_monthly?: number | null
          profile_picture_url?: string | null
          role?: Database["public"]["Enums"]["user_role"] | null
          social_instagram?: string | null
          social_twitter?: string | null
          social_website?: string | null
          stripe_account_id?: string | null
          stripe_onboarding_complete?: boolean | null
          subscriber_count?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      videos: {
        Row: {
          bunny_video_id: string
          created_at: string | null
          creator_id: string | null
          encoding_status: string | null
          id: string
          is_free: boolean | null
          post_id: string | null
          title: string | null
          updated_at: string | null
          view_count: number | null
        }
        Insert: {
          bunny_video_id: string
          created_at?: string | null
          creator_id?: string | null
          encoding_status?: string | null
          id?: string
          is_free?: boolean | null
          post_id?: string | null
          title?: string | null
          updated_at?: string | null
          view_count?: number | null
        }
        Update: {
          bunny_video_id?: string
          created_at?: string | null
          creator_id?: string | null
          encoding_status?: string | null
          id?: string
          is_free?: boolean | null
          post_id?: string | null
          title?: string | null
          updated_at?: string | null
          view_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "videos_creator_id_fkey"
            columns: ["creator_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "videos_post_id_fkey"
            columns: ["post_id"]
            isOneToOne: false
            referencedRelation: "diary_entries"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      consume_credit_for_post: {
        Args: { reader_uuid: string; entry_uuid: string }
        Returns: boolean
      }
      get_entry_preview: {
        Args: { entry_id: string; viewer_id?: string }
        Returns: {
          id: string
          title: string
          body_md: string
          is_free: boolean
          is_hidden: boolean
          created_at: string
          user_id: string
          writer_id: string
          writer_name: string
          writer_price: number
          can_read_full: boolean
        }[]
      }
      get_follower_count: {
        Args: { p_writer_id: string }
        Returns: number
      }
      get_top_diary_entries_hourly: {
        Args: { limit_count?: number }
        Returns: {
          entry_id: string
          title: string
          writer_name: string
          writer_id: string
          writer_avatar: string
          hourly_loves: number
          total_loves: number
          first_photo_url: string
          created_at: string
          is_free: boolean
        }[]
      }
      get_writer_locked_entries: {
        Args: { writer_uuid: string }
        Returns: {
          entry_id: string
          title: string
          body_teaser: string
          created_at: string
        }[]
      }
      get_writer_public_data: {
        Args: { writer_uuid: string }
        Returns: {
          writer_id: string
          writer_name: string
          writer_bio: string
          writer_avatar: string
          writer_price_monthly: number
          free_entry_id: string
          free_entry_title: string
          free_entry_body: string
          free_entry_created_at: string
        }[]
      }
      is_following: {
        Args: { p_writer_id: string }
        Returns: boolean
      }
      update_entry_keywords: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      user_can_comment_on_entry: {
        Args: { user_uuid: string; entry_uuid: string }
        Returns: boolean
      }
      user_can_read_post: {
        Args: { reader_uuid: string; entry_uuid: string }
        Returns: boolean
      }
      user_has_active_subscription: {
        Args: { subscriber_uuid: string; writer_uuid: string }
        Returns: boolean
      }
    }
    Enums: {
      moderation_status: "pending" | "approved" | "flagged" | "rejected"
      payment_kind: "sub" | "donation"
      user_role: "visitor" | "subscriber" | "writer" | "admin"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      moderation_status: ["pending", "approved", "flagged", "rejected"],
      payment_kind: ["sub", "donation"],
      user_role: ["visitor", "subscriber", "writer", "admin"],
    },
  },
} as const
