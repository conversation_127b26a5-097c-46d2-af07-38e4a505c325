import Stripe from 'stripe'

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set')
}

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2024-12-18.acacia',
  typescript: true,
})

// Platform fee percentages
export const SUBSCRIPTION_FEE_PERCENTAGE = 0.20 // 20% for subscriptions
export const DONATION_FEE_PERCENTAGE = 0.05 // 5% for donations

// Calculate platform fee from total amount
export function calculatePlatformFee(totalAmount: number, paymentType: 'subscription' | 'donation' = 'subscription'): {
  platformFee: number
  writerAmount: number
} {
  const feePercentage = paymentType === 'donation' ? DONATION_FEE_PERCENTAGE : SUBSCRIPTION_FEE_PERCENTAGE
  const platformFee = Math.round(totalAmount * feePercentage)
  const writerAmount = totalAmount - platformFee

  return {
    platformFee,
    writerAmount
  }
}

// Minimum amounts (in cents)
export const MIN_SUBSCRIPTION_AMOUNT = 299 // $2.99
export const MAX_SUBSCRIPTION_AMOUNT = 5000 // $50.00
export const MIN_DONATION_AMOUNT = 100 // $1.00
export const MAX_DONATION_AMOUNT = 10000 // $100.00

// Format price for display
export function formatPrice(cents: number): string {
  return `$${(cents / 100).toFixed(2)}`
}
