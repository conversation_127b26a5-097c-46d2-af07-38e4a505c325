"use client"

import { useState } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import Link from "next/link"
import { Button } from "@/components/ui/button"

interface Book {
  id: string
  title: string
  description: string
  cover_image_url: string
  genre: string
  book_type: string
  price_amount: number
  average_rating: number
  review_count: number
  sales_count: number
  is_ebook: boolean
  is_complete: boolean
  is_private: boolean
  created_at: string
  updated_at: string
}

interface BookManagerProps {
  book: Book
  onUpdate: () => void
}

export function BookManager({ book, onUpdate }: BookManagerProps) {
  const [loading, setLoading] = useState(false)
  const [showSendToStore, setShowSendToStore] = useState(false)
  const supabase = createSupabaseClient()

  const formatPrice = (cents: number) => {
    if (cents === 0) return "Free"
    return `$${(cents / 100).toFixed(2)}`
  }

  const sendToStore = async () => {
    setLoading(true)
    try {
      const { error } = await supabase
        .from('projects')
        .update({
          is_ebook: true,
          is_complete: true,
          is_private: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', book.id)

      if (error) throw error

      setShowSendToStore(false)
      onUpdate()
    } catch (error) {
      console.error('Error sending book to store:', error)
    } finally {
      setLoading(false)
    }
  }

  const removeFromStore = async () => {
    setLoading(true)
    try {
      const { error } = await supabase
        .from('projects')
        .update({
          is_ebook: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', book.id)

      if (error) throw error

      onUpdate()
    } catch (error) {
      console.error('Error removing book from store:', error)
    } finally {
      setLoading(false)
    }
  }

  const togglePrivacy = async () => {
    setLoading(true)
    try {
      const { error } = await supabase
        .from('projects')
        .update({
          is_private: !book.is_private,
          updated_at: new Date().toISOString()
        })
        .eq('id', book.id)

      if (error) throw error

      onUpdate()
    } catch (error) {
      console.error('Error updating privacy:', error)
    } finally {
      setLoading(false)
    }
  }

  const isInStore = book.is_ebook && book.is_complete && !book.is_private

  return (
    <div className="border border-gray-200 rounded-lg p-6 bg-white">
      <div className="flex items-start gap-4">
        {book.cover_image_url ? (
          <img 
            src={book.cover_image_url} 
            alt={book.title}
            className="w-20 h-28 object-cover rounded shadow-sm"
          />
        ) : (
          <div className="w-20 h-28 bg-gray-200 rounded flex items-center justify-center">
            <span className="text-3xl">📖</span>
          </div>
        )}
        
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between mb-2">
            <h3 className="text-lg font-medium text-gray-900 truncate">{book.title}</h3>
            <div className="flex items-center gap-2 ml-4">
              {isInStore && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  📚 In Store
                </span>
              )}
              {book.is_private && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  🔒 Private
                </span>
              )}
              {!book.is_complete && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  ✏️ Draft
                </span>
              )}
            </div>
          </div>
          
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">{book.description}</p>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-sm">
            <div>
              <span className="text-gray-500">Price:</span>
              <p className="font-medium">{formatPrice(book.price_amount)}</p>
            </div>
            <div>
              <span className="text-gray-500">Sales:</span>
              <p className="font-medium">{book.sales_count || 0}</p>
            </div>
            <div>
              <span className="text-gray-500">Rating:</span>
              <p className="font-medium">
                {book.average_rating ? `${book.average_rating.toFixed(1)}/10` : 'No ratings'}
              </p>
            </div>
            <div>
              <span className="text-gray-500">Reviews:</span>
              <p className="font-medium">{book.review_count || 0}</p>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Link href={`/write/projects/${book.id}`}>
              <Button size="sm" variant="outline">
                ✏️ Edit
              </Button>
            </Link>
            
            {isInStore ? (
              <>
                <Link href={`/books/${book.id}`}>
                  <Button size="sm" variant="outline">
                    👁️ View in Store
                  </Button>
                </Link>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={removeFromStore}
                  disabled={loading}
                >
                  📤 Remove from Store
                </Button>
              </>
            ) : (
              <Button 
                size="sm" 
                className="bg-purple-600 text-white hover:bg-purple-700"
                onClick={() => setShowSendToStore(true)}
                disabled={!book.is_complete}
              >
                📚 Send to Store
              </Button>
            )}
            
            <Button 
              size="sm" 
              variant="outline"
              onClick={togglePrivacy}
              disabled={loading}
            >
              {book.is_private ? '🔓 Make Public' : '🔒 Make Private'}
            </Button>
          </div>
        </div>
      </div>

      {/* Send to Store Confirmation Modal */}
      {showSendToStore && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Send Book to Store</h3>
            <p className="text-gray-600 mb-4">
              This will make your book available for purchase in the OnlyDiary Book Store. 
              You'll earn 80% of all sales revenue*.
            </p>
            <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
              <p className="text-sm text-green-800">
                <strong>💰 Author Royalties:</strong> You keep 80% of all sales*
              </p>
              <p className="text-xs text-green-600 mt-1">
                * After Stripe processing fees (2.9% + 30¢ per transaction)
              </p>
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setShowSendToStore(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={sendToStore}
                disabled={loading}
                className="flex-1 bg-purple-600 text-white hover:bg-purple-700"
              >
                {loading ? 'Sending...' : 'Send to Store'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
