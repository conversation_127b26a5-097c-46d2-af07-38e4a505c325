"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { createSupabaseClient } from "@/lib/supabase/client"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

interface BestsellerBook {
  id: string
  title: string
  cover_image_url: string
  price_amount: number
  genre: string
  daily_sales: number
  total_sales: number
  average_rating: number
  total_reviews: number
  users: {
    name: string
    avatar_url: string
  }
}

interface BestsellerChartProps {
  timeframe?: 'daily' | 'weekly' | 'monthly' | 'all-time'
  limit?: number
  showHeader?: boolean
}

export function BestsellerChart({ 
  timeframe = 'daily', 
  limit = 10, 
  showHeader = true 
}: BestsellerChartProps) {
  const [books, setBooks] = useState<BestsellerBook[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedTimeframe, setSelectedTimeframe] = useState(timeframe)
  const supabase = createSupabaseClient()

  useEffect(() => {
    fetchBestsellers()
  }, [selectedTimeframe])

  const fetchBestsellers = async () => {
    setLoading(true)
    try {
      let dateFilter = ''
      const now = new Date()
      
      switch (selectedTimeframe) {
        case 'daily':
          const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          dateFilter = today.toISOString()
          break
        case 'weekly':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          dateFilter = weekAgo.toISOString()
          break
        case 'monthly':
          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          dateFilter = monthAgo.toISOString()
          break
        case 'all-time':
          dateFilter = '1970-01-01T00:00:00.000Z'
          break
      }

      // Query to get bestselling books with sales data
      const { data, error } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          cover_image_url,
          price_amount,
          genre,
          users!inner(name, avatar_url),
          book_purchases!inner(created_at),
          book_reviews(rating)
        `)
        .eq('is_ebook', true)
        .gte('book_purchases.created_at', dateFilter)
        .order('created_at', { ascending: false })

      if (error) throw error

      // Process the data to calculate sales and ratings
      const processedBooks = data?.map(book => {
        const sales = book.book_purchases?.length || 0
        const reviews = book.book_reviews || []
        const averageRating = reviews.length > 0 
          ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length 
          : 0

        return {
          id: book.id,
          title: book.title,
          cover_image_url: book.cover_image_url,
          price_amount: book.price_amount,
          genre: book.genre,
          daily_sales: sales,
          total_sales: sales, // For now, using same value
          average_rating: averageRating,
          total_reviews: reviews.length,
          users: book.users
        }
      }) || []

      // Sort by sales count and limit results
      const sortedBooks = processedBooks
        .sort((a, b) => b.daily_sales - a.daily_sales)
        .slice(0, limit)

      setBooks(sortedBooks)
    } catch (error) {
      console.error('Error fetching bestsellers:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatPrice = (cents: number) => {
    if (cents === 0) return 'Free'
    return `$${(cents / 100).toFixed(2)}`
  }

  const renderPenRating = (rating: number) => {
    if (rating === 0) return <span className="text-gray-400 text-xs">No ratings</span>
    
    const fullPens = Math.floor(rating)
    const hasHalfPen = rating % 1 >= 0.5
    
    return (
      <div className="flex items-center gap-1">
        {[...Array(10)].map((_, i) => (
          <span
            key={i}
            className={`text-xs ${
              i < fullPens 
                ? 'text-purple-600' 
                : i === fullPens && hasHalfPen 
                  ? 'text-purple-400' 
                  : 'text-gray-300'
            }`}
          >
            🖊️
          </span>
        ))}
        <span className="text-xs text-gray-600 ml-1">
          {rating.toFixed(1)}
        </span>
      </div>
    )
  }

  const getTimeframeLabel = () => {
    switch (selectedTimeframe) {
      case 'daily': return 'Today\'s Bestsellers'
      case 'weekly': return 'This Week\'s Bestsellers'
      case 'monthly': return 'This Month\'s Bestsellers'
      case 'all-time': return 'All-Time Bestsellers'
      default: return 'Bestsellers'
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {showHeader && (
          <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
        )}
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-24 bg-gray-200 rounded animate-pulse"></div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {showHeader && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h2 className="text-2xl font-serif text-gray-900">
            📈 {getTimeframeLabel()}
          </h2>
          
          <div className="flex gap-2">
            {(['daily', 'weekly', 'monthly', 'all-time'] as const).map((period) => (
              <Button
                key={period}
                variant={selectedTimeframe === period ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedTimeframe(period)}
                className={selectedTimeframe === period ? "bg-purple-600 text-white" : ""}
              >
                {period === 'all-time' ? 'All Time' : period.charAt(0).toUpperCase() + period.slice(1)}
              </Button>
            ))}
          </div>
        </div>
      )}

      {books.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-4xl mb-3">📊</div>
            <h3 className="text-lg font-serif text-gray-800 mb-2">
              No sales data yet
            </h3>
            <p className="text-gray-600">
              Check back once books start selling!
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {books.map((book, index) => (
            <Card key={book.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  {/* Rank */}
                  <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold text-purple-600">
                      #{index + 1}
                    </span>
                  </div>

                  {/* Book Cover */}
                  <div className="w-12 h-16 bg-gray-100 rounded overflow-hidden flex-shrink-0">
                    {book.cover_image_url ? (
                      <img
                        src={book.cover_image_url}
                        alt={book.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400 text-xs">
                        📚
                      </div>
                    )}
                  </div>

                  {/* Book Info */}
                  <div className="flex-1 min-w-0">
                    <Link 
                      href={`/books/${book.id}`}
                      className="block hover:text-purple-600 transition-colors"
                    >
                      <h3 className="font-medium text-gray-900 truncate">
                        {book.title}
                      </h3>
                    </Link>
                    <p className="text-sm text-gray-600 truncate">
                      by {book.users.name}
                    </p>
                    <div className="flex items-center gap-3 mt-1">
                      {renderPenRating(book.average_rating)}
                      {book.genre && (
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          {book.genre}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Sales & Price */}
                  <div className="text-right flex-shrink-0">
                    <div className="text-lg font-bold text-purple-600">
                      {book.daily_sales}
                    </div>
                    <div className="text-xs text-gray-500">
                      {selectedTimeframe === 'daily' ? 'today' : 'sales'}
                    </div>
                    <div className="text-sm font-medium text-gray-900 mt-1">
                      {formatPrice(book.price_amount)}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {books.length > 0 && (
        <div className="text-center">
          <Link href="/books?sort=bestsellers">
            <Button variant="outline">
              View All Bestsellers →
            </Button>
          </Link>
        </div>
      )}
    </div>
  )
}
