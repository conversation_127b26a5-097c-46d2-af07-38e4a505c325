"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter, usePathname } from "next/navigation"
import { createSupabaseClient } from "@/lib/supabase/client"

export function Navigation() {
  const [user, setUser] = useState<{ id: string; email: string; role: string; name?: string } | null>(null)
  const [loading, setLoading] = useState(true) // Add loading state
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [navigating, setNavigating] = useState<string | null>(null) // Track which button is loading
  const router = useRouter()
  const pathname = usePathname()
  const supabase = createSupabaseClient()

  useEffect(() => {
    let mounted = true

    const fetchUser = async () => {
      setLoading(true)
      const { data: { session } } = await supabase.auth.getSession()
      if (mounted && session?.user) {
        const { data: profile } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single()
        setUser(profile || { id: session.user.id, email: session.user.email, role: 'writer' })
      } else {
        setUser(null)
      }
      if (mounted) {
        setLoading(false)
      }
    }

    fetchUser()

    const { data: { subscription } } = supabase.auth.onAuthStateChange(() => {
      if (mounted) {
        fetchUser()
      }
    })

    return () => {
      mounted = false
      subscription?.unsubscribe()
    }
  }, [supabase])


  const handleNavClick = (path: string) => {
    setNavigating(path)
    setMobileMenuOpen(false) // Close mobile menu
    router.push(path)

    // Auto-reset loading after timeout in case navigation fails
    setTimeout(() => {
      setNavigating(null)
    }, 5000)
  }

  // Reset navigating state when pathname changes (navigation completes)
  useEffect(() => {
    setNavigating(null)
  }, [pathname])

  const handleSignOut = async () => {
    try {
      console.log('Signing out...')

      // Clear auth state immediately
      await supabase.auth.signOut()

      // Clear any local storage
      localStorage.clear()
      sessionStorage.clear()

      console.log('Sign out complete, redirecting...')

      // Force immediate redirect
      window.location.replace('/')

    } catch (error) {
      console.error('Sign out error:', error)

      // Force clear everything and redirect anyway
      localStorage.clear()
      sessionStorage.clear()
      window.location.replace('/')
    }
  }

  return (
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link href="/" className="text-2xl font-serif text-gray-800 hover:text-gray-600 transition-colors">
              OnlyDiary
            </Link>
          </div>

          <div className="hidden md:flex items-center space-x-8">
            <button
              onClick={() => handleNavClick('/trending')}
              disabled={navigating === '/trending'}
              className="text-gray-600 hover:text-gray-800 font-medium cursor-pointer disabled:opacity-50 flex items-center gap-2"
            >
              {navigating === '/trending' ? (
                <>
                  <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                  <span>🔥 Trending</span>
                </>
              ) : (
                '🔥 Trending'
              )}
            </button>

            {!loading && user ? (
              <>
                {user?.role === 'subscriber' ? (
                  <>
                    <button
                      onClick={() => handleNavClick('/discover')}
                      disabled={navigating === '/discover'}
                      className="text-gray-600 hover:text-gray-800 font-medium cursor-pointer disabled:opacity-50 flex items-center gap-2"
                    >
                      {navigating === '/discover' ? (
                        <>
                          <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                          <span>Discover</span>
                        </>
                      ) : (
                        'Discover'
                      )}
                    </button>
                    <button
                      onClick={() => handleNavClick('/timeline')}
                      disabled={navigating === '/timeline'}
                      className="text-gray-600 hover:text-gray-800 font-medium cursor-pointer disabled:opacity-50 flex items-center gap-2"
                    >
                      {navigating === '/timeline' ? (
                        <>
                          <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                          <span>Timeline</span>
                        </>
                      ) : (
                        'Timeline'
                      )}
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={() => handleNavClick('/dashboard')}
                      disabled={navigating === '/dashboard'}
                      className="text-gray-600 hover:text-gray-800 font-medium cursor-pointer disabled:opacity-50 flex items-center gap-2"
                    >
                      {navigating === '/dashboard' ? (
                        <>
                          <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                          <span>Dashboard</span>
                        </>
                      ) : (
                        'Dashboard'
                      )}
                    </button>
                    <button
                      onClick={() => handleNavClick('/write')}
                      disabled={navigating === '/write'}
                      className="text-gray-600 hover:text-gray-800 font-medium cursor-pointer disabled:opacity-50 flex items-center gap-2"
                    >
                      {navigating === '/write' ? (
                        <>
                          <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                          <span>Create</span>
                        </>
                      ) : (
                        'Create'
                      )}
                    </button>
                    <button
                      onClick={() => handleNavClick('/timeline')}
                      disabled={navigating === '/timeline'}
                      className="text-gray-600 hover:text-gray-800 font-medium cursor-pointer disabled:opacity-50 flex items-center gap-2"
                    >
                      {navigating === '/timeline' ? (
                        <>
                          <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                          <span>📖 Timeline</span>
                        </>
                      ) : (
                        '📖 Timeline'
                      )}
                    </button>
                  </>
                )}

                <div className="relative group">
                  <button className="flex items-center gap-1 text-gray-600 hover:text-gray-800 font-medium">
                    {user.name || 'Profile'}
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                    {/* Timeline - for everyone who might follow creators */}
                    <Link
                      href="/timeline"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                    >
                      <span>📖</span>
                      Timeline
                    </Link>

                    {/* Discover - for finding new creators */}
                    <Link
                      href="/discover"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                    >
                      <span>🔍</span>
                      Explore Creators
                    </Link>

                    <div className="border-t border-gray-100 my-1"></div>

                    {/* Admin Panel - only for admins */}
                    {user?.role === 'admin' && (
                      <Link
                        href="/admin"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                      >
                        <span>⚙️</span>
                        Admin Panel
                      </Link>
                    )}

                    <Link
                      href={user?.role === 'subscriber' ? `/s/${user.id}` : "/profile"}
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                    >
                      <span>👤</span>
                      Profile
                    </Link>

                    <button
                      onClick={handleSignOut}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                    >
                      <span>🚪</span>
                      Sign Out
                    </button>
                  </div>
                </div>
              </>
            ) : !loading && (
              <>
                <button
                  onClick={() => handleNavClick('/login')}
                  disabled={navigating === '/login'}
                  className="text-gray-600 hover:text-gray-800 font-medium cursor-pointer disabled:opacity-50 flex items-center gap-2"
                >
                  {navigating === '/login' ? (
                    <>
                      <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                      <span>Sign In</span>
                    </>
                  ) : (
                    'Sign In'
                  )}
                </button>
                <button
                  onClick={() => handleNavClick('/register')}
                  disabled={navigating === '/register'}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors cursor-pointer disabled:opacity-50 flex items-center gap-2"
                >
                  {navigating === '/register' ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Get Started</span>
                    </>
                  ) : (
                    'Get Started'
                  )}
                </button>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="text-gray-600 hover:text-gray-800 p-2"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
              <button
                onClick={() => handleNavClick('/trending')}
                disabled={navigating === '/trending'}
                className="block w-full text-left py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium disabled:opacity-50"
              >
                {navigating === '/trending' ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                    <span>🔥 Trending</span>
                  </div>
                ) : (
                  '🔥 Trending'
                )}
              </button>

              {user ? (
                <>
                  {user?.role === 'subscriber' ? (
                    <>
                      <Link
                        href="/discover"
                        className="block py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        Discover
                      </Link>
                      <Link
                        href="/timeline"
                        className="block py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        Timeline
                      </Link>
                    </>
                  ) : (
                    <>
                      <Link
                        href="/dashboard"
                        className="block py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        Dashboard
                      </Link>
                      <Link
                        href="/write"
                        className="block py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        Create
                      </Link>
                      <Link
                        href="/timeline"
                        className="block py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        📖 Timeline
                      </Link>
                    </>
                  )}

                  {/* Universal options for all users */}
                  {user?.role === 'subscriber' && (
                    <Link
                      href="/discover"
                      className="block py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      🔍 Explore Creators
                    </Link>
                  )}

                  <div className="border-t border-gray-200 my-2"></div>

                  {/* Admin Panel - only for admins */}
                  {user?.role === 'admin' && (
                    <Link
                      href="/admin"
                      className="block py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      ⚙️ Admin Panel
                    </Link>
                  )}

                  <Link
                    href={user?.role === 'subscriber' ? `/s/${user.id}` : "/profile"}
                    className="block py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    👤 Profile
                  </Link>
                  <button
                    onClick={() => {
                      handleSignOut()
                      setMobileMenuOpen(false)
                    }}
                    className="block w-full text-left py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium"
                  >
                    🚪 Sign Out
                  </button>
                </>
              ) : (
                <>
                  <Link
                    href="/login"
                    className="block py-3 px-4 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors font-medium"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Sign In
                  </Link>
                  <Link
                    href="/register"
                    className="block py-3 px-4 bg-blue-600 text-white hover:bg-blue-700 rounded-lg transition-colors font-medium"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Get Started
                  </Link>
                </>
              )}
            </div>
          </div>
        )}
        </div>
      </nav>
  )
}
