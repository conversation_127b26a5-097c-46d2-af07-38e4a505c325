'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Search, Heart, BookOpen, Users } from 'lucide-react'

interface ReaderDashboardProps {
  user: any
  subscriptions: any[]
  recentEntries: any[]
}

export function ReaderDashboard({ user, subscriptions, recentEntries }: ReaderDashboardProps) {
  const [searchQuery, setSearchQuery] = useState('')

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-6 py-8">
        
        {/* Welcome Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-serif text-gray-800 mb-2">
            Welcome back, {user.name}
          </h1>
          <p className="text-gray-600 font-serif">
            Discover new stories and connect with your favorite creators
          </p>
        </div>

        {/* Search Bar */}
        <div className="mb-8">
          <div className="relative max-w-2xl">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search for creators, stories, or topics..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-4 bg-white rounded-2xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
            />
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-2xl p-6 shadow-sm">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-800">{subscriptions.length}</h3>
                <p className="text-gray-600 text-sm">Creators Following</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-sm">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <BookOpen className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-800">{recentEntries.length}</h3>
                <p className="text-gray-600 text-sm">New Stories</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-sm">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center">
                <Heart className="w-6 h-6 text-pink-600" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-800">0</h3>
                <p className="text-gray-600 text-sm">Flowers Received</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Main Content - Recent Stories */}
          <div className="lg:col-span-2 space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-serif text-gray-800">Latest Stories</h2>
              <Link 
                href="/discover" 
                className="text-blue-600 hover:text-blue-700 font-medium text-sm"
              >
                Discover More →
              </Link>
            </div>

            {recentEntries.length > 0 ? (
              <div className="space-y-6">
                {recentEntries.map((entry: any) => (
                  <div key={entry.id} className="bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0">
                        {entry.user.avatar ? (
                          <img
                            src={entry.user.avatar}
                            alt={entry.user.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <span className="text-lg font-serif text-gray-500">
                            {entry.user.name.charAt(0).toUpperCase()}
                          </span>
                        )}
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-medium text-gray-900">{entry.user.name}</h3>
                          <span className="text-gray-400">•</span>
                          <span className="text-gray-500 text-sm">
                            {new Date(entry.created_at).toLocaleDateString()}
                          </span>
                          {entry.is_free && (
                            <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-medium">
                              FREE
                            </span>
                          )}
                        </div>
                        
                        <h4 className="text-lg font-serif text-gray-800 mb-3">{entry.title}</h4>
                        
                        <p className="text-gray-600 leading-relaxed mb-4">
                          {entry.body_md.slice(0, 200)}...
                        </p>
                        
                        <Link
                          href={`/d/${entry.id}`}
                          className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
                        >
                          Continue Reading →
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-white rounded-2xl p-12 text-center shadow-sm">
                <BookOpen className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-serif text-gray-800 mb-2">No Stories Yet</h3>
                <p className="text-gray-600 mb-6">
                  Start following creators to see their latest stories here
                </p>
                <Link
                  href="/discover"
                  className="inline-block bg-blue-600 text-white px-6 py-3 rounded-xl font-medium hover:bg-blue-700 transition-colors"
                >
                  Discover Creators
                </Link>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            
            {/* Following */}
            <div className="bg-white rounded-2xl p-6 shadow-sm">
              <h3 className="text-lg font-serif text-gray-800 mb-4">Following</h3>
              
              {subscriptions.length > 0 ? (
                <div className="space-y-3">
                  {subscriptions.slice(0, 5).map((sub: any) => (
                    <Link
                      key={sub.id}
                      href={`/u/${sub.writer.id}`}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                        {sub.writer.avatar ? (
                          <img
                            src={sub.writer.avatar}
                            alt={sub.writer.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <span className="text-sm font-serif text-gray-500">
                            {sub.writer.name.charAt(0).toUpperCase()}
                          </span>
                        )}
                      </div>
                      <span className="text-gray-800 font-medium text-sm">{sub.writer.name}</span>
                    </Link>
                  ))}
                  
                  {subscriptions.length > 5 && (
                    <Link
                      href="/subscriptions"
                      className="block text-center text-blue-600 hover:text-blue-700 text-sm font-medium pt-2"
                    >
                      View All ({subscriptions.length})
                    </Link>
                  )}
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-gray-500 text-sm mb-3">Not following anyone yet</p>
                  <Link
                    href="/discover"
                    className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                  >
                    Find Creators →
                  </Link>
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-2xl p-6 shadow-sm">
              <h3 className="text-lg font-serif text-gray-800 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <Link
                  href="/discover"
                  className="block w-full text-left p-3 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="font-medium text-gray-800">Discover Creators</div>
                  <div className="text-gray-500 text-sm">Find new voices to follow</div>
                </Link>
                
                <Link
                  href="/subscriptions"
                  className="block w-full text-left p-3 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="font-medium text-gray-800">My Subscriptions</div>
                  <div className="text-gray-500 text-sm">Manage your follows</div>
                </Link>
                
                <Link
                  href="/profile"
                  className="block w-full text-left p-3 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="font-medium text-gray-800">My Flowers</div>
                  <div className="text-gray-500 text-sm">See flowers from creators</div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
