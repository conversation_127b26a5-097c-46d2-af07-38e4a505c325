-- Create projects table for book writing projects
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    cover_image_url TEXT,
    genre TEXT,
    is_private BOOLEAN DEFAULT FALSE,
    is_complete BOOLEAN DEFAULT FALSE,
    price_type TEXT CHECK (price_type IN ('project', 'chapters')) DEFAULT 'project',
    price_amount INTEGER CHECK (price_amount >= 0), -- Price in cents
    total_chapters INTEGER DEFAULT 0,
    total_words INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create chapters table for individual chapters within projects
CREATE TABLE chapters (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    content TEXT DEFAULT '',
    chapter_number INTEGER NOT NULL,
    word_count INTEGER DEFAULT 0,
    is_published BOOLEAN DEFAULT FALSE,
    love_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique chapter numbers per project
    UNIQUE(project_id, chapter_number)
);

-- Add indexes for performance
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_projects_created_at ON projects(created_at DESC);
CREATE INDEX idx_projects_updated_at ON projects(updated_at DESC);
CREATE INDEX idx_projects_is_private ON projects(is_private);

CREATE INDEX idx_chapters_project_id ON chapters(project_id);
CREATE INDEX idx_chapters_user_id ON chapters(user_id);
CREATE INDEX idx_chapters_chapter_number ON chapters(project_id, chapter_number);
CREATE INDEX idx_chapters_is_published ON chapters(is_published);

-- Enable RLS
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE chapters ENABLE ROW LEVEL SECURITY;

-- RLS Policies for projects
-- Writers can manage their own projects
CREATE POLICY "Writers can manage their own projects" ON projects
    FOR ALL USING (auth.uid() = user_id);

-- Anyone can view public projects
CREATE POLICY "Anyone can view public projects" ON projects
    FOR SELECT USING (NOT is_private);

-- Admins can view all projects
CREATE POLICY "Admins can view all projects" ON projects
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- RLS Policies for chapters
-- Writers can manage their own chapters
CREATE POLICY "Writers can manage their own chapters" ON chapters
    FOR ALL USING (auth.uid() = user_id);

-- Anyone can view published chapters from public projects
CREATE POLICY "Anyone can view published chapters from public projects" ON chapters
    FOR SELECT USING (
        is_published = true AND 
        EXISTS (
            SELECT 1 FROM projects 
            WHERE id = chapters.project_id AND is_private = false
        )
    );

-- Admins can view all chapters
CREATE POLICY "Admins can view all chapters" ON chapters
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Add triggers for updated_at
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_chapters_updated_at BEFORE UPDATE ON chapters
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update project stats when chapters change
CREATE OR REPLACE FUNCTION update_project_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Update total_chapters and total_words for the project
    UPDATE projects 
    SET 
        total_chapters = (
            SELECT COUNT(*) 
            FROM chapters 
            WHERE project_id = COALESCE(NEW.project_id, OLD.project_id)
        ),
        total_words = (
            SELECT COALESCE(SUM(word_count), 0) 
            FROM chapters 
            WHERE project_id = COALESCE(NEW.project_id, OLD.project_id)
        )
    WHERE id = COALESCE(NEW.project_id, OLD.project_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Triggers to update project stats
CREATE TRIGGER update_project_stats_on_chapter_insert
    AFTER INSERT ON chapters
    FOR EACH ROW EXECUTE FUNCTION update_project_stats();

CREATE TRIGGER update_project_stats_on_chapter_update
    AFTER UPDATE ON chapters
    FOR EACH ROW EXECUTE FUNCTION update_project_stats();

CREATE TRIGGER update_project_stats_on_chapter_delete
    AFTER DELETE ON chapters
    FOR EACH ROW EXECUTE FUNCTION update_project_stats();

-- Create storage bucket for project covers
INSERT INTO storage.buckets (id, name, public)
VALUES ('project-covers', 'project-covers', true);

-- Storage policies for project covers
CREATE POLICY "Anyone can view project covers" ON storage.objects
    FOR SELECT USING (bucket_id = 'project-covers');

CREATE POLICY "Authenticated users can upload project covers" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'project-covers' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Users can update project covers" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'project-covers' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Users can delete project covers" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'project-covers' AND
        auth.role() = 'authenticated'
    );
